"""
Langbase Python SDK.

This package provides a Python interface to the Langbase API, allowing you to
build and deploy AI-powered applications using Langbase's infrastructure.
```
"""

from .errors import (
    APIConnectionError,
    APIConnectionTimeoutError,
    APIError,
    AuthenticationError,
    BadRequestError,
    ConflictError,
    InternalServerError,
    NotFoundError,
    PermissionDeniedError,
    RateLimitError,
    UnprocessableEntityError,
)
from .helper import (
    ChoiceStream,
    ChunkStream,
    Delta,
    StreamProcessor,
    collect_stream_text,
    create_stream_processor,
    get_runner,
    get_text_part,
    get_tools_from_run,
    get_tools_from_run_stream,
    get_tools_from_stream,
    get_typed_runner,
    handle_response_stream,
    parse_chunk,
    stream_text,
)
from .langbase import Langbase
from .primitives.memories import Memories
from .primitives.pipes import Pipes
from .primitives.threads import Threads
from .primitives.tools import Tools
from .streaming import StreamEventType, TypedStreamProcessor
from .types import (
    ChoiceGenerate,
    Message,
    MessageContentItem,
    PipeBaseOptions,
    PipeBaseResponse,
    PipeCreateOptions,
    PipeCreateResponse,
    PipeListResponse,
    PipeUpdateOptions,
    PipeUpdateResponse,
    ResponseFormat,
    RunResponse,
    RunResponseStream,
    ToolCall,
    ToolChoice,
    Usage,
    Variable,
)
from .workflow import TimeoutError, Workflow

__version__ = "0.0.3"
__author__ = "LangbaseInc"
__description__ = "Python SDK for the Langbase API"

__all__ = [
    # Errors
    "APIConnectionError",
    "APIConnectionTimeoutError",
    "APIError",
    "AuthenticationError",
    "BadRequestError",
    # Type definitions
    "ChoiceGenerate",
    # Helper utilities
    "ChunkStream",
    "ConflictError",
    "InternalServerError",
    # Main classes
    "Langbase",
    "Memories",
    "Message",
    "NotFoundError",
    "PermissionDeniedError",
    "PipeBaseOptions",
    "PipeBaseResponse",
    "PipeCreateOptions",
    "PipeCreateResponse",
    "PipeListResponse",
    "PipeUpdateOptions",
    "PipeUpdateResponse",
    "Pipes",
    "RateLimitError",
    "ResponseFormat",
    "RunResponse",
    "RunResponseStream",
    # Streaming
    "StreamEventType",
    "StreamProcessor",
    "Threads",
    "TimeoutError",
    "ToolCall",
    "ToolChoice",
    "Tools",
    "Tools",
    "TypedStreamProcessor",
    "UnprocessableEntityError",
    "Usage",
    "Variable",
    "Workflow",
    "collect_stream_text",
    "create_stream_processor",
    "get_runner",
    "get_text_part",
    "get_tools_from_run",
    "get_tools_from_run_stream",
    "get_tools_from_stream",
    "get_typed_runner",
    "handle_response_stream",
    "parse_chunk",
    "stream_text",
]
