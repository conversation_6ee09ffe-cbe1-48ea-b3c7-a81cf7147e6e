[project]
name = "langbase"
version = "0.0.3"
description = "Python SDK for the Langbase API"
readme = "README.md"
license = { text = "Apache-2.0" }
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON> I<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>ki<PERSON>", email = "<EMAIL>" },
    { name = "Saqib Ameen", email = "<EMAIL>" },
]
requires-python = ">=3.7"
keywords = [
    "ai",
    "langbase",
    "agent",
    "memory",
    "rag",
    "mcp",
    "pipes",
    "workflow",
    "llms",
]
classifiers = [
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3 :: Only",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = ["requests>=2.25.0", "typing-extensions>=4.0.0"]

[project.optional-dependencies]

[project.urls]
Documentation = "https://docs.langbase.com"
Homepage = "https://langbase.com"
Repository = "https://github.com/LangbaseInc/langbase-python-sdk"
Issues = "https://github.com/LangbaseInc/langbase-python-sdk/issues"

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
include = ["langbase*"]

[tool.black]
line-length = 88
target-version = ["py37", "py38", "py39", "py310", "py311", "py312"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 88
known_first_party = ["langbase"]
skip_glob = ["*/venv/*", "*/.venv/*"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = "-v --strict-markers --tb=short"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["langbase"]
branch = true
omit = ["*/tests/*", "*/__init__.py", "*/venv/*", "*/.venv/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
    "@abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"
